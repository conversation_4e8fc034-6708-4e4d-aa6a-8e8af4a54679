# Multi-Tenant Admin System API Documentation

## Base URL
```
http://localhost:8001
```

## Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <access_token>
```

---

## 🔐 Authentication Endpoints

### POST /auth/login
**Description**: Authenticate user and get access token

**Request**:
- **Content-Type**: `application/x-www-form-urlencoded`
- **Body**:
  ```
  username=admin&password=admin
  ```

**Response**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "user": {
    "id": "685d23e959562f1c51026225",
    "username": "admin",
    "role": "admin"
  }
}
```

**Status Codes**:
- `200`: Success
- `401`: Invalid credentials

---

### GET /verify_token
**Description**: Verify if the current token is valid

**Headers**:
```
Authorization: Bearer <access_token>
```

**Response**:
```json
true
```

**Status Codes**:
- `200`: Token is valid
- `401`: Token is invalid/expired

---

## 👥 User Management Endpoints

### POST /create_user
**Description**: Create a new user (Admin only)

**Headers**:
```
Authorization: Bearer <access_token>
Content-Type: application/json
```

**Request Body**:
```json
{
  "username": "newuser",
  "password": "securepassword",
  "role": "user"
}
```

**Response**:
```json
{
  "success": true,
  "message": "User created successfully",
  "user_id": "685d24a159562f1c51026226",
  "username": "newuser",
  "role": "user"
}
```

**Role Options**:
- `admin`: Full system access
- `supervisor`: Can view users and tenants
- `user`: Can only view tenants

**Status Codes**:
- `200`: User created successfully
- `400`: Invalid role or user already exists
- `401`: Unauthorized
- `403`: Insufficient permissions (not admin)

---

## 🏢 Tenant Management Endpoints

### POST /tenants/create
**Description**: Create a new tenant organization (Admin only)

**Headers**:
```
Authorization: Bearer <access_token>
Content-Type: application/json
```

**Request Body**:
```json
{
  "name": "My Company",
  "slug": "mycompany"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Tenant created successfully",
  "tenant_id": "685d25b259562f1c51026227",
  "tenant_name": "My Company",
  "tenant_slug": "mycompany",
  "database_name": "mycompany_db"
}
```

**Status Codes**:
- `200`: Tenant created successfully
- `400`: Tenant with slug already exists
- `401`: Unauthorized
- `403`: Insufficient permissions (not admin)

---

### GET /tenants/list
**Description**: List all tenants (Admin only)

**Headers**:
```
Authorization: Bearer <access_token>
```

**Response**:
```json
{
  "success": true,
  "tenants": [
    {
      "_id": "6786473b4b4e998b38776c13",
      "name": "Four Semetrons",
      "slug": "fsem",
      "database_name": "fsem_db"
    },
    {
      "_id": "685d25b259562f1c51026227",
      "name": "My Company",
      "slug": "mycompany",
      "database_name": "mycompany_db"
    }
  ],
  "total": 2
}
```

**Status Codes**:
- `200`: Success
- `401`: Unauthorized
- `403`: Insufficient permissions (not admin)

---

## 📊 Data Models

### User Model
```typescript
interface User {
  id: string;
  username: string;
  role: "admin" | "supervisor" | "user";
}
```

### Tenant Model
```typescript
interface Tenant {
  _id: string;
  name: string;
  slug: string;
  database_name: string;
}
```

### Login Request
```typescript
interface LoginRequest {
  username: string;
  password: string;
}
```

### Login Response
```typescript
interface LoginResponse {
  access_token: string;
  token_type: "bearer";
  user: User;
}
```

### Create User Request
```typescript
interface CreateUserRequest {
  username: string;
  password: string;
  role: "admin" | "supervisor" | "user";
}
```

### Create User Response
```typescript
interface CreateUserResponse {
  success: boolean;
  message: string;
  user_id: string;
  username: string;
  role: string;
}
```

### Create Tenant Request
```typescript
interface CreateTenantRequest {
  name: string;
  slug: string;
}
```

### Create Tenant Response
```typescript
interface CreateTenantResponse {
  success: boolean;
  message: string;
  tenant_id: string;
  tenant_name: string;
  tenant_slug: string;
  database_name: string;
}
```

### List Tenants Response
```typescript
interface ListTenantsResponse {
  success: boolean;
  tenants: Tenant[];
  total: number;
}
```

---

## 🔒 Role-Based Permissions

| Role | Permissions |
|------|-------------|
| **admin** | • Create users<br>• View users<br>• Create tenants<br>• View tenants |
| **supervisor** | • View users<br>• View tenants |
| **user** | • View tenants |

---

## ❌ Error Responses

### 401 Unauthorized
```json
{
  "detail": "Could not validate credentials"
}
```

### 403 Forbidden
```json
{
  "detail": "Permission denied. Required permission: user.create"
}
```

### 400 Bad Request
```json
{
  "detail": "User with this username already exists"
}
```

### 500 Internal Server Error
```json
{
  "detail": "Error creating user: <error_message>"
}
```

---

## 🚀 Frontend Integration Examples

### JavaScript/TypeScript Example

```typescript
// Login
const login = async (username: string, password: string) => {
  const formData = new FormData();
  formData.append('username', username);
  formData.append('password', password);
  
  const response = await fetch('/auth/login', {
    method: 'POST',
    body: formData
  });
  
  return await response.json();
};

// Create User
const createUser = async (userData: CreateUserRequest, token: string) => {
  const response = await fetch('/create_user', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(userData)
  });
  
  return await response.json();
};

// List Tenants
const listTenants = async (token: string) => {
  const response = await fetch('/tenants/list', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return await response.json();
};
```

---

## 📝 Notes for Frontend Developers

1. **Token Storage**: Store the access token securely (localStorage/sessionStorage)
2. **Token Expiry**: Tokens expire after 120 minutes
3. **Error Handling**: Always check response status codes
4. **Role Checks**: Implement role-based UI hiding/showing
5. **CORS**: The API supports CORS for frontend origins
6. **Content-Type**: Use `application/json` for JSON requests
7. **Form Data**: Login endpoint expects `application/x-www-form-urlencoded`

---

## 🔧 Development Setup

**Default Admin Credentials**:
- Username: `admin`
- Password: `admin`

**Available Roles for Testing**:
- `admin`: Full access
- `supervisor`: Read-only access to users and tenants  
- `user`: Read-only access to tenants only
