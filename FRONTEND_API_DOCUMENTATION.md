# Frontend API Documentation

## Base URL
```
http://127.0.0.1:8000
```

## Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <access_token>
```

---

## 🔐 Authentication Endpoints

### POST /auth/login
**Description**: Authenticate user and get access token

**Request Body**:
```json
{
  "username": "admin",
  "password": "password123",
  "client_id": "tenant_slug"
}
```

**Response** (200 OK):
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "user": {
    "id": "user_id_here",
    "username": "admin",
    "role": "admin"
  }
}
```

**Error Responses**:
- `401`: Invalid credentials
- `404`: Tenant not found

---

## 🏢 Tenant Management Endpoints

### GET /tenants/list
**Description**: Get list of all tenants (Admin only)

**Headers**:
```
Authorization: Bearer <access_token>
```

**Response** (200 OK):
```json
{
  "success": true,
  "tenants": [
    {
      "id": "tenant_id_1",
      "name": "Company A",
      "slug": "company-a",
      "database_name": "eko_company-a_db",
      "created_at": "2024-01-15T10:30:00Z"
    },
    {
      "id": "tenant_id_2", 
      "name": "Company B",
      "slug": "company-b",
      "database_name": "eko_company-b_db",
      "created_at": "2024-01-16T14:20:00Z"
    }
  ]
}
```

**Error Responses**:
- `401`: Unauthorized (invalid token)
- `403`: Forbidden (not admin)

### GET /tenants/{tenant_id}
**Description**: Get specific tenant details by ID

**Headers**:
```
Authorization: Bearer <access_token>
```

**Path Parameters**:
- `tenant_id` (string): The tenant ID

**Response** (200 OK):
```json
{
  "success": true,
  "tenant": {
    "id": "tenant_id_1",
    "name": "Company A",
    "slug": "company-a", 
    "database_name": "eko_company-a_db",
    "created_at": "2024-01-15T10:30:00Z",
    "user_count": 15,
    "status": "active"
  }
}
```

**Error Responses**:
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Tenant not found

### POST /create-tenent
**Description**: Create a new tenant with default configuration

**Headers**:
```
Authorization: Bearer <access_token>
Content-Type: application/json
```

**Request Body**:
```json
{
  "tenant_name": "New Company",
  "tenant_slug": "new-company",
  "product": "685d2c9bb23fc531edf6dd0c",
  "username": "admin"
}
```

**Response** (200 OK):
```json
{
  "success": true,
  "message": "Tenant created successfully",
  "tenant_id": "6861074f0abb0ea55a6153dc",
  "tenant_slug": "new-company",
  "database_name": "eko_new-company_db",
  "invitation_link": "https://eko.nextai.asia/invitation?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&username=admin&role=admin"
}
```

**Error Responses**:
- `400`: Invalid input (missing required fields, invalid product ID)
- `401`: Unauthorized
- `403`: Forbidden
- `500`: Server error (tenant creation failed)

---

## 📱 Frontend Implementation Guide

### 1. Login Screen Implementation

```javascript
// Login API call
const login = async (username, password, clientId) => {
  try {
    const response = await fetch('/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username,
        password,
        client_id: clientId
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      // Store token in localStorage or secure storage
      localStorage.setItem('access_token', data.access_token);
      localStorage.setItem('user', JSON.stringify(data.user));
      return data;
    } else {
      throw new Error('Login failed');
    }
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};
```

### 2. Tenant List Page Implementation

```javascript
// Fetch tenants list
const fetchTenants = async () => {
  try {
    const token = localStorage.getItem('access_token');
    const response = await fetch('/tenants/list', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      return data.tenants;
    } else {
      throw new Error('Failed to fetch tenants');
    }
  } catch (error) {
    console.error('Fetch tenants error:', error);
    throw error;
  }
};

// Tenant list component example
const TenantList = () => {
  const [tenants, setTenants] = useState([]);
  
  useEffect(() => {
    fetchTenants().then(setTenants);
  }, []);
  
  return (
    <div>
      <h2>Tenants</h2>
      {tenants.map(tenant => (
        <div key={tenant.id} onClick={() => selectTenant(tenant.id)}>
          <h3>{tenant.name}</h3>
          <p>Slug: {tenant.slug}</p>
          <p>Created: {new Date(tenant.created_at).toLocaleDateString()}</p>
        </div>
      ))}
    </div>
  );
};
```

### 3. Tenant Selection Implementation

```javascript
// Get specific tenant details
const getTenantDetails = async (tenantId) => {
  try {
    const token = localStorage.getItem('access_token');
    const response = await fetch(`/tenants/${tenantId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      return data.tenant;
    } else {
      throw new Error('Failed to fetch tenant details');
    }
  } catch (error) {
    console.error('Fetch tenant details error:', error);
    throw error;
  }
};

// Handle tenant selection
const selectTenant = async (tenantId) => {
  try {
    const tenantDetails = await getTenantDetails(tenantId);
    // Store selected tenant or navigate to tenant dashboard
    localStorage.setItem('selected_tenant', JSON.stringify(tenantDetails));
    // Navigate to tenant dashboard
    window.location.href = `/dashboard/${tenantDetails.slug}`;
  } catch (error) {
    console.error('Tenant selection error:', error);
  }
};
```

### 4. Create Tenant Page Implementation

```javascript
// Create new tenant
const createTenant = async (tenantData) => {
  try {
    const token = localStorage.getItem('access_token');
    const response = await fetch('/create-tenent', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(tenantData)
    });
    
    if (response.ok) {
      const data = await response.json();
      return data;
    } else {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to create tenant');
    }
  } catch (error) {
    console.error('Create tenant error:', error);
    throw error;
  }
};

// Create tenant form component
const CreateTenantForm = () => {
  const [formData, setFormData] = useState({
    tenant_name: '',
    tenant_slug: '',
    product: '685d2c9bb23fc531edf6dd0c', // Default product ID
    username: 'admin'
  });
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const result = await createTenant(formData);
      
      // Show success message with invitation link
      alert(`Tenant created successfully! Invitation link: ${result.invitation_link}`);
      
      // Optionally redirect to the invitation link
      if (confirm('Redirect to invitation link?')) {
        window.open(result.invitation_link, '_blank');
      }
      
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        placeholder="Tenant Name"
        value={formData.tenant_name}
        onChange={(e) => setFormData({...formData, tenant_name: e.target.value})}
        required
      />
      <input
        type="text"
        placeholder="Tenant Slug"
        value={formData.tenant_slug}
        onChange={(e) => setFormData({...formData, tenant_slug: e.target.value})}
        required
      />
      <input
        type="text"
        placeholder="Username"
        value={formData.username}
        onChange={(e) => setFormData({...formData, username: e.target.value})}
        required
      />
      <button type="submit">Create Tenant</button>
    </form>
  );
};
```

### 5. Invitation Link Handling

```javascript
// Handle invitation link redirect
const handleInvitationLink = (invitationLink) => {
  // Option 1: Open in new tab
  window.open(invitationLink, '_blank');
  
  // Option 2: Redirect current window
  // window.location.href = invitationLink;
  
  // Option 3: Copy to clipboard
  navigator.clipboard.writeText(invitationLink).then(() => {
    alert('Invitation link copied to clipboard!');
  });
};

// Parse invitation link components
const parseInvitationLink = (link) => {
  const url = new URL(link);
  return {
    token: url.searchParams.get('token'),
    username: url.searchParams.get('username'),
    role: url.searchParams.get('role')
  };
};
```

---

## 🔒 Error Handling

### Common Error Responses

```javascript
// Generic error handler
const handleApiError = (response) => {
  switch (response.status) {
    case 401:
      // Redirect to login
      localStorage.removeItem('access_token');
      window.location.href = '/login';
      break;
    case 403:
      alert('You do not have permission to perform this action');
      break;
    case 404:
      alert('Resource not found');
      break;
    case 500:
      alert('Server error. Please try again later.');
      break;
    default:
      alert('An unexpected error occurred');
  }
};
```

### Token Refresh (if implemented)

```javascript
// Check if token is expired and refresh if needed
const ensureValidToken = async () => {
  const token = localStorage.getItem('access_token');
  if (!token) {
    throw new Error('No token found');
  }
  
  // Decode JWT to check expiration (you'll need a JWT library)
  const payload = JSON.parse(atob(token.split('.')[1]));
  const isExpired = payload.exp * 1000 < Date.now();
  
  if (isExpired) {
    // Redirect to login or refresh token
    localStorage.removeItem('access_token');
    window.location.href = '/login';
  }
  
  return token;
};
```

---

## 📋 Frontend Checklist

### Login Screen
- [ ] Username/password input fields
- [ ] Client ID (tenant slug) input field
- [ ] Login button with loading state
- [ ] Error message display
- [ ] Token storage after successful login

### Tenant List Page
- [ ] Display list of tenants with name, slug, created date
- [ ] Click handler for tenant selection
- [ ] Loading state while fetching
- [ ] Error handling for failed requests
- [ ] Admin-only access control

### Tenant Selection
- [ ] Fetch and display tenant details
- [ ] Store selected tenant information
- [ ] Navigate to tenant-specific dashboard
- [ ] Handle selection errors

### Create Tenant Page
- [ ] Form with tenant name, slug, username fields
- [ ] Form validation
- [ ] Submit button with loading state
- [ ] Success message with invitation link
- [ ] Option to redirect to invitation link
- [ ] Error handling and display

### General
- [ ] Consistent error handling across all pages
- [ ] Token expiration handling
- [ ] Loading states for all API calls
- [ ] Responsive design for mobile devices
- [ ] Proper navigation between pages
