from fastapi import Form
from fastapi.security import OAuth2PasswordRequestForm

class OAuth2PasswordRequestFormWithClientID(OAuth2PasswordRequestForm):
    def __init__(
        self,
        grant_type: str = Form(None, regex="password"),
        username: str = Form(...),
        password: str = Form(...),
        scope: str = Form(""),
        client_id: str = Form(...),  # Add client_id as a required field
    ):
        super().__init__(grant_type=grant_type, username=username, password=password, scope=scope)
        self.client_id = client_id