from pydantic import BaseModel, Field, field_validator
from typing import Any, Literal,Optional, Dict, Union
    

class User(BaseModel):
    """
    User document from the tenant database -> users collection
    """
    id: Any = Field(alias="_id")
    username: str
    role: Literal["admin", "supervisor", "user"]

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)


class UserTenantDB(BaseModel):
    tenant_id: str
    tenant_database_name: str
    slug: str
    db: Any
    user: User
    async_db: Any
    access: Optional[Dict] = None
    db_client: Any

class ExtendedTokenRequest(BaseModel):
    username: str
    password: str
    client_id: str
    days: Union[int, float] = Field(..., description="Number of days for token validity", ge=1, le=365)

class AgentInvitation(BaseModel):
    username: Optional[str] = Field(..., example="agent_username")
    role: Literal["admin", "supervisor", "agent"]
    permissions: Dict[str, bool]| None={"all":True}
    expires_at: int = Field(default=7, description="Expiration in days")
    tenant_id:Optional[str]=None
    slug:Optional[str]=None
    