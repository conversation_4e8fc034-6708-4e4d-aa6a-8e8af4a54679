# app/core/permissions.py

from typing import List, Dict, Any, Optional
from fastapi import HTTPException, Depends
from app.models.user import UserTenantDB
from app.core.security import user_tenant_info
from app.core.database import get_admin_db

class PermissionChecker:
    """Class to handle permission checking logic with database initialization"""

    def __init__(self, admin_db):
        """Initialize with admin database instance"""
        self.admin_db = admin_db

    async def get_role_level(self, role: str) -> int:
        """Get the level of a role from database"""
        role_doc = await self.admin_db.roles.find_one({"name": role})
        return role_doc.get("level", 0) if role_doc else 0

    async def get_role_permissions(self, role: str) -> List[str]:
        """Get all permissions for a role from database"""
        role_doc = await self.admin_db.roles.find_one({"name": role})
        return role_doc.get("permissions", []) if role_doc else []

    async def has_permission(self, user_role: str, required_permission: str) -> bool:
        """Check if a user role has a specific permission"""
        role_permissions = await self.get_role_permissions(user_role)
        return required_permission in role_permissions

    async def has_min_role_level(self, user_role: str, min_level: int) -> bool:
        """Check if user role meets minimum level requirement"""
        user_level = await self.get_role_level(user_role)
        return user_level >= min_level

    async def can_manage_role(self, manager_role: str, target_role: str) -> bool:
        """Check if a manager can manage a target role (must be higher level)"""
        manager_level = await self.get_role_level(manager_role)
        target_level = await self.get_role_level(target_role)
        return manager_level > target_level

def require_permission(permission: str):
    """Decorator to require a specific permission"""
    async def permission_dependency(user_tenant_info: UserTenantDB = Depends(user_tenant_info)):
        # Get permission checker from database
        checker = await get_permission_checker()
        has_perm = await checker.has_permission(user_tenant_info.user.role, permission)
        if not has_perm:
            raise HTTPException(
                status_code=403,
                detail=f"Permission denied. Required permission: {permission}"
            )
        return user_tenant_info
    return permission_dependency

def require_role_level(min_level: int):
    """Decorator to require a minimum role level"""
    async def role_level_dependency(user_tenant_info: UserTenantDB = Depends(user_tenant_info)):
        # Get permission checker from database
        checker = await get_permission_checker()
        user_level = await checker.get_role_level(user_tenant_info.user.role)
        if user_level < min_level:
            raise HTTPException(
                status_code=403,
                detail=f"Access denied. Minimum role level required: {min_level}"
            )
        return user_tenant_info
    return role_level_dependency

def require_role(allowed_roles: List[str]):
    """Decorator to require one of the specified roles"""
    async def role_dependency(user_tenant_info: UserTenantDB = Depends(user_tenant_info)):
        if user_tenant_info.user.role not in allowed_roles:
            raise HTTPException(
                status_code=403,
                detail=f"Access denied. Required roles: {', '.join(allowed_roles)}"
            )
        return user_tenant_info
    return role_dependency

def require_admin():
    """Decorator to require admin role - only admins can create users and tenants"""
    return require_role(["admin"])

# Global permission checker instance
_permission_checker = None

async def get_permission_checker():
    """Get or create permission checker instance"""
    global _permission_checker
    if _permission_checker is None:
        admin_db = await get_admin_db()
        _permission_checker = PermissionChecker(admin_db)
    return _permission_checker
