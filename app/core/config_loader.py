# app/core/config_loader.py

from typing import List, Optional
from app.core.database import get_admin_db

# Global configuration cache
_config_cache = {}

async def load_config_from_db():
    """Load configuration from database"""
    global _config_cache
    
    admin_db = await get_admin_db()
    
    # Load settings from database
    settings = await admin_db.settings.find_one({"name": "system_config"})
    
    if settings:
        _config_cache = settings.get("config", {})
    else:
        # Create default settings if they don't exist
        default_config = {
            "secret_key": "your-super-secret-key-change-this-in-production",
            "allowed_origins": [
                "http://localhost:3000",
                "http://localhost:8404", 
                "http://localhost:6970"
            ],
            "token_expire_minutes": 120
        }
        
        await admin_db.settings.insert_one({
            "name": "system_config",
            "config": default_config
        })
        
        _config_cache = default_config
    
    return _config_cache

async def get_secret_key() -> str:
    """Get secret key from database"""
    if not _config_cache:
        await load_config_from_db()
    return _config_cache.get("secret_key", "fallback-secret-key")

async def get_allowed_origins() -> List[str]:
    """Get allowed origins from database"""
    if not _config_cache:
        await load_config_from_db()
    return _config_cache.get("allowed_origins", ["http://localhost:3000"])

async def get_token_expire_minutes() -> int:
    """Get token expiration time from database"""
    if not _config_cache:
        await load_config_from_db()
    return _config_cache.get("token_expire_minutes", 120)

async def update_config(key: str, value):
    """Update a configuration value in database"""
    global _config_cache
    
    admin_db = await get_admin_db()
    
    # Update cache
    _config_cache[key] = value
    
    # Update database
    await admin_db.settings.update_one(
        {"name": "system_config"},
        {"$set": {f"config.{key}": value}},
        upsert=True
    )

async def add_allowed_origin(origin: str):
    """Add a new allowed origin"""
    origins = await get_allowed_origins()
    if origin not in origins:
        origins.append(origin)
        await update_config("allowed_origins", origins)

async def remove_allowed_origin(origin: str):
    """Remove an allowed origin"""
    origins = await get_allowed_origins()
    if origin in origins:
        origins.remove(origin)
        await update_config("allowed_origins", origins)
