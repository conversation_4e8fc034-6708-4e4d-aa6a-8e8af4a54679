# app/core/security.py

from datetime import datetime, timedelta
from argon2 import <PERSON><PERSON><PERSON>asher
from argon2.exceptions import VerifyMismatchError
from fastapi.security import OAuth2<PERSON><PERSON>word<PERSON>earer
from fastapi import Depends, HTTPException
from bson import ObjectId

import jwt
from app.core.config import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.core.config_loader import get_secret_key, get_token_expire_minutes
from app.models.user import UserTenantDB, User
from app.core.database import get_async_db_from_tenant_id, get_admin_db, get_async_client
from typing import Optional


# Use Argon2 for password hashing (more secure than bcrypt)
pwd_hasher = PasswordHasher()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/login")

async def create_access_token(data: dict, expires_delta: timedelta = None):
    secret_key = await get_secret_key()
    token_expire_minutes = await get_token_expire_minutes()

    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now() + expires_delta
    else:
        expire = datetime.now() + timedelta(minutes=token_expire_minutes)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, secret_key, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        SECRET_KEY = await get_secret_key()
        # Decode the JWT token
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except jwt.PyJWTError:
        raise credentials_exception

    tenant_db = await get_async_db_from_tenant_id(payload.get("tenant_id"))
    user = await tenant_db.users.find_one({"username": username})
    if user is None:
        raise credentials_exception
    return user

async def user_tenant_info(token: str = Depends(oauth2_scheme)) -> UserTenantDB:
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        secret_key = await get_secret_key()
        payload = jwt.decode(token, secret_key, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except jwt.PyJWTError:
        raise credentials_exception

    # For admin system, we use admin database instead of tenant database
    admin_db = await get_admin_db()
    db_client =await get_async_client()
    user = await admin_db.users.find_one({"username": username})
    if user is None:
        raise credentials_exception

    # Return with admin_db as the database (no tenant_id needed)
    return UserTenantDB(
        tenant_id="admin",
        tenant_database_name="multi_tenant_admin",
        slug="admin",
        db=admin_db,
        user=User(**user),
        db_client=db_client,
        async_db=admin_db
    )


# min_role function moved to app.core.permissions for better organization

def hash_password(plain_password: str) -> str:
    """
    Hash a plain password using Argon2.
    """
    return pwd_hasher.hash(plain_password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a plain password against an Argon2 hashed password.
    """
    try:
        pwd_hasher.verify(hashed_password, plain_password)
        return True
    except VerifyMismatchError:
        return False

async def create_invitation_token(username: str, role: str, invited_by: str, tenant_id:str, expires_delta: Optional[timedelta] = None):
    """
    Creates a JWT token for agent invitation.
    """
    secret_key = await get_secret_key()
    to_encode = {"username": username, "invited_by": invited_by, "role": role, "tenant_id": tenant_id}
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=7)  # Default expiration: 7 days
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, secret_key, algorithm=ALGORITHM)
    return encoded_jwt

async def verify_invitation_token(token: str):
    """
    Verifies the invitation token and extracts the agent's name.
    """
    try:
        secret_key = await get_secret_key()
        payload = jwt.decode(token, secret_key, algorithms=[ALGORITHM])
        username: str = payload.get("username")
        invited_by: str = payload.get("invited_by")
        role: str = payload.get("role")

        tenant_db = await get_async_db_from_tenant_id(payload.get("tenant_id"))
        result = await tenant_db.invitations.find_one({"username": username, "role": role})

        if username is None or invited_by is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        if result is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        return username, invited_by, role

    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=400, detail="Invitation token has expired")
    except jwt.PyJWTError:
        raise HTTPException(status_code=400, detail="Invalid invitation token")








async def get_tenant_info(token: str = Depends(oauth2_scheme)) -> UserTenantDB:
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        SECRET_KEY = await get_secret_key()
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        print("payloaddd",payload)
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except jwt.PyJWTError:
        raise credentials_exception
    print("payload",payload.get("tenant_id"))

    tenant_db =await get_async_db_from_tenant_id(payload.get("tenant_id"))
    async_tenant_db = get_async_db_from_tenant_id(payload.get("tenant_id"))
    user = tenant_db.users.find_one({"username": username})
    if user is None:
        raise credentials_exception
    tenant_database_name = get_admin_db().tenants.find_one({"_id": ObjectId(payload.get("tenant_id"))})["slug"]
    slug = get_admin_db().tenants.find_one({"_id": ObjectId(payload.get("tenant_id"))})["slug"]
    user_role = user.get("role")
    user_access = tenant_db.settings.find_one({"name": "nav_access"})
    role_access = user_access.get(user_role, {}) if user_access else {}
    return UserTenantDB(
        tenant_id=payload.get("tenant_id"),
        tenant_database_name=tenant_database_name, 
        slug=slug,
        db=tenant_db, 
        user=User(**user),
        async_db=async_tenant_db, 
        access=role_access
    )