from datetime import datetime, <PERSON><PERSON><PERSON>
from dotenv import load_dotenv
import os
from pymongo import AsyncMongoClient
from pydantic import BaseModel
from typing import Optional, Literal
from bson import ObjectId

from app.core.security import create_invitation_token
from app.utils import convert_objectid_to_str

load_dotenv()
class UserRegistration(BaseModel):
    """Model for user registration data"""
    username: str
    password: str
    role: Optional[Literal["admin", "supervisor", "agent"]] = "admin"


class Tenant:
    """
    Tenant management class for creating and configuring new tenants.

    Handles:
    - Tenant registration in admin database
    - Client database creation and setup
    - Default data insertion
    - Environment configuration
    """
    def __init__(self,name,slug,username,requirements,product_db):
        self.name=name
        self.slug=slug
        self.username=username
        self.mongo_url= os.getenv("MONGO_URI")
        self.admin_db=product_db
        self.requirements=requirements

    def _get_required_list(self,type):
        return self.requirements[type]
    
    def _admin_db_connection(self):
        """to get admin database connection"""
        # return AsyncMongoClient(self.mongo_url)["eko_admin"] #[self.admin_db]
        return self.admin_db


    def _prepare_tenant(self):
        """prepare tenant data"""
        name=self.name
        slug=self.slug.replace(" ", "_")
        database_name=f"eko_{slug}_db"
        label=self.name.title()
        
        return {
            "name":name,
            "slug":slug,
            "database_name":database_name,
            "label":label,
            "topic_generation":False
        }
    
    async def _register_tenant(self):
        """ to register tenant in admin database"""
        tenant_data = self._prepare_tenant()
        client = self._admin_db_connection()
        collections = await client.list_collection_names()
        if "tenants" in collections:
            collection = client["tenants"]
        else:
            collection = await client.create_collection("tenants")
        
        if self.slug in [tenant["slug"] for tenant in await collection.find().to_list(length=10)]:
            raise Exception(f"Tenant with slug '{self.slug}' already exists in '{self.admin_db}")
        
        result = await collection.insert_one(tenant_data)
        # print id of tenant
        print(result.inserted_id)
        if result.acknowledged:
            print("Tenant registered successfully")
        else:
            raise Exception("Tenant registration failed")

        return True
    
    async def _get_tenant_id(self):
        """Get tenant id from slug and return as string"""
        client = self._admin_db_connection()
        print(f"Looking for tenant with slug: {self.slug}")
        result = await client.tenants.find_one({"slug": self.slug})
        if result:
            tenant_id = str(result["_id"])  # Convert ObjectId to string
            print(f"Found tenant with ID: {tenant_id}")
            return tenant_id
        else:
            # List all available tenants for debugging
            all_tenants = await client.tenants.find().to_list(length=10)
            print(f"Available tenants: {[t.get('slug') for t in all_tenants]}")
            raise Exception(f"Tenant with slug '{self.slug}' not found")

    async def _prepare_client_database(self):
        """
        for creating database and collections for client
        """
        tenant_data = self._prepare_tenant()
        database_name = tenant_data["database_name"]
        client = AsyncMongoClient(self.mongo_url)
        database_names = await client.list_database_names()
        if database_name in database_names:
            raise Exception(f"{database_name} Database already exists")
    
        client_db = client[database_name]
        
        collection=["users","tools","settings","prompt","invitations"]
        for col in collection:
            collection_names = await client_db.list_collection_names()
            if col in collection_names:
                continue
            else:
                await client_db.create_collection(col)
                print(f"{col} collection created successfully")

        print("Database created successfully")
        return True
        
    async def _get_client_db(self):
        """to get client database"""
        tenant_data = self._prepare_tenant()
        database_name = tenant_data["database_name"]
        client = AsyncMongoClient(self.mongo_url)
        database_names = await client.list_database_names()
        if database_name in database_names:
            return client[database_name]
        else:
            raise Exception(f"{database_name} Database does not exist")
    
    async def _prepare_env(self):
        """Prepare environment configuration for tenant"""
        env_schema = await self._admin_db_connection()["settings"].find_one({"name": "env"})
        if not env_schema:
            raise Exception("Environment schema not found in admin database")

        # Convert ObjectIds to strings for JSON serialization
        env_schema = convert_objectid_to_str(env_schema)

        # Update tenant-specific configurations
        env_schema["qdrant_config"]["coll_name"] = f"{self.slug}_sentence_context"
        env_schema["qdrant_config"]["sentence_collection"] = f"{self.slug}_sentence_context"
        env_schema["qdrant_config"]["page_collection"] = f"{self.slug}_test_page_info"
        env_schema["qdrant_config"]["sentence_split_collection"] = f"{self.slug}_sentence_split"
        env_schema["minio_config"]["bucket_name"] = f"eko.{self.slug}"

        return env_schema
    
    async def _insert_default_data(self):
        """Insert default data into client database"""
        admin_db = self.admin_db
        print("admin_db", admin_db)

        # Get default superadmin user
        default_user = await admin_db["users"].find_one({"username": "superadmin"})
        if not default_user:
            raise Exception("Default superadmin user not found in admin database")


        client_db = await self._get_client_db()
        result = await client_db.users.insert_one(default_user)
        if not result.acknowledged:
            raise Exception("Default user insertion failed")
  
 
        # Insert default prompts
        cursor = admin_db["prompt"].find({"name": {"$in": self._get_required_list("required_prompt")}})
        default_prompts = await cursor.to_list()
        if default_prompts:
            # Convert ObjectIds to strings
            default_prompts = [convert_objectid_to_str(prompt) for prompt in default_prompts]
            result = await client_db.prompt.insert_many(default_prompts)
            if not result.acknowledged:
                raise Exception("Default prompt insertion failed")

        # Insert default tools
        cursor = admin_db["tools"].find({"name": {"$in": self._get_required_list("required_tools")}})
        default_tools = await cursor.to_list()
        if default_tools:
            # Convert ObjectIds to strings
            default_tools = [convert_objectid_to_str(tool) for tool in default_tools]
            result = await client_db.tools.insert_many(default_tools)
            if not result.acknowledged:
                raise Exception("Default tools insertion failed")

        # Insert default settings
        cursor = admin_db["settings"].find({"name": {"$in": self._get_required_list("required_settings")}})
        default_settings = await cursor.to_list(length=100)
        if default_settings:
            # Convert ObjectIds to strings
            default_settings = [convert_objectid_to_str(setting) for setting in default_settings]
            result = await client_db.settings.insert_many(default_settings)
            if not result.acknowledged:
                raise Exception("Default settings insertion failed")

        env_schema = await self._prepare_env()
 
        result = await client_db.settings.insert_one(env_schema)
        if not result.acknowledged:
            raise Exception("env settings insertion failed")
        
        # Create invitation token and save invitation record
        client_tenant_id = await self._get_tenant_id()
        print("client_tenant_id", client_tenant_id)

        superadmin = await client_db.users.find_one({"username": "superadmin"})
        if not superadmin:
            raise Exception("Superadmin user not found in client database")

        # Create invitation token
        invite_token = await create_invitation_token(username=" ",
                                    role="admin",
                                    invited_by=str(superadmin["_id"]),
                                    tenant_id=client_tenant_id,
                                    expires_delta=timedelta(days=365))

        # Save invitation record in invitations collection
        invitation_record = {
            "username": " ",  # Empty username for initial admin invitation
            "slug": self.slug,
            "token": invite_token,
            "role": "admin",
            "invited_by": str(superadmin["_id"]),
            "expires_at": datetime.now() + timedelta(days=365),
            "used": False,
            "permissions": {"all": True}
        }

        # Insert invitation record
        result = await client_db.invitations.insert_one(invitation_record)
        if not result.acknowledged:
            raise Exception("Invitation record insertion failed")

        print("invite_token", invite_token)

        link = f"https://eko.nextai.asia/invitation?token={invite_token}&username={self.username}&role=admin"

        print("Default data insertion completed")
        return link
