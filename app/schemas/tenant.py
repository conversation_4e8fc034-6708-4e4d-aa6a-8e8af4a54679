# app/schemas/tenant.py

from pydantic import BaseModel, Field
from typing import Optional

class TenantCreate(BaseModel):
    name: str = Field(..., description="The name of the tenant organization")
    slug: str = Field(..., description="The unique slug for the tenant (used in URLs)")
    product: str = Field(..., description="The product or service associated with the tenant")

class TenantResponse(BaseModel):
    success: bool
    message: str
    tenant_id: Optional[str] = None
    tenant_name: Optional[str] = None
    tenant_slug: Optional[str] = None
    database_name: Optional[str] = None
