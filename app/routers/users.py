from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRe<PERSON>Form
from bson.objectid import ObjectId
from app.utils import convert_objectid_to_str
from app.core.database import get_tenant_id_from_slug
from app.models.user import UserTenantDB,ExtendedTokenRequest

from app.core.database import get_async_db_from_tenant_id

from app.core.security import (
    create_access_token,
    verify_password,
    hash_password,
    create_invitation_token,
    verify_invitation_token,
    user_tenant_info
)

from app.core.permissions import (
    require_admin,
    
)

from app.schemas.user import (
    UserCreate,
    UserResponse
)

from datetime import timedelta, datetime, timezone

from app.core.database import get_admin_db


router = APIRouter(tags=["Users"])




@router.get("/verify_token")
async def verify_token(user_tenant_info: UserTenantDB = Depends(user_tenant_info)):
    """If the token is valid, return the user's details
       If the token is invalid, get_current_user will raise an exception
    """
    return True


@router.post("/create_user", response_model=UserResponse)
async def create_user(
    user_data: UserCreate,
    current_user: UserTenantDB = Depends(require_admin())
):
    """
    Create a new user in the specified tenant.
    Only admins can create users.
    """
    try:
        # Get admin database (no tenant needed for admin system)
        admin_db = await get_admin_db()

        # Check if user already exists
        existing_user = await admin_db.users.find_one({"username": user_data.username})
        if existing_user:
            raise HTTPException(
                status_code=400,
                detail="User with this username already exists"
            )

        # Only allow creating admin, supervisor, or user roles in this admin system
        if user_data.role not in ["admin", "supervisor", "user"]:
            raise HTTPException(
                status_code=400,
                detail="Invalid role. Only 'admin', 'supervisor', and 'user' roles are allowed in this system"
            )

        # Hash the password using Argon2
        hashed_password = hash_password(user_data.password)

        # Create user document
        user_doc = {
            "username": user_data.username,
            "role": user_data.role,
            "hashed_password": hashed_password,
            "created_at": datetime.utcnow(),
            "created_by": current_user.user.username,
            "is_active": True
        }

        # Insert user into admin database
        result = await admin_db.users.insert_one(user_doc)

        return UserResponse(
            success=True,
            message="User created successfully",
            user_id=str(result.inserted_id),
            username=user_data.username,
            role=user_data.role
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating user: {str(e)}")


@router.post("/auth/login")
async def authenticate_user(form_data: OAuth2PasswordRequestForm = Depends()):
    """
    Authenticate user and return access token.
    This is for the admin database only - no tenant slug needed.
    """
    try:
        # Get admin database
        admin_db = await get_admin_db()

        # Find user in admin database
        user = await admin_db.users.find_one({"username": form_data.username})

        if not user or not verify_password(form_data.password, user["hashed_password"]):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # result =await  get_tenant_id_from_slug(form_data.client_id)
        # if not result:
        #     raise HTTPException(status_code=404, detail="Tenant not found")
        # tenant_id = str(result["_id"])

        # Create access token (no tenant_id needed for admin system)
        access_token = await create_access_token(
            data={"sub": user["username"], "role": user["role"]},
            expires_delta=timedelta(minutes=120)
        )

        user = convert_objectid_to_str(user)

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": user["_id"],
                "username": user['username'],
                "role": user['role']
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Authentication error: {str(e)}")




@router.post("/extended_token")
async def get_extended_token(request: ExtendedTokenRequest):
    """Generate an access token with extended validity period.

    Args:
        request: The request containing username, password, client_id, and days for token validity

    Returns:
        A dictionary containing the access token and user information
    """
    # Find the tenant database
    result = await get_tenant_id_from_slug(request.client_id)

    print("User authenticated")
    if not result:
        raise HTTPException(status_code=404, detail="Tenant not found")

    tenant_id = str(result["_id"])
    tenant_database = get_async_db_from_tenant_id(tenant_id)

    # Authenticate the user
    user = tenant_database.users.find_one({"username": request.username})
    if not user:
        raise HTTPException(status_code=401, detail="User not found")

    if not verify_password(request.password, user["hashed_password"]):
        raise HTTPException(status_code=401, detail="Incorrect credentials")
    
    print("User authenticated successfully")

    # Generate token with extended validity
    access_token = create_access_token(
        data={"sub": user["username"], "user_id": str(user["_id"]), "role": user["role"], "tenant_id": tenant_id},
        expires_delta=timedelta(days=request.days)
    )

    # Get navigation permissions if available
    try:
        nav_permission = tenant_database.settings.find_one({"name": "nav_permission"}).get(user["role"])
    except Exception:
        nav_permission = None

    # Convert ObjectId to string for JSON serialization
    user = convert_objectid_to_str(user)

    # Log the extended token generation
    # loggers.info(f"Extended token generated for user {request.username} with validity of {request.days} days")

    return {
        "id": user["_id"],
        "access_token": access_token,
        "token_type": "bearer",
        "username": user['username'],
        "role": user['role'],
        "tenant_id": tenant_id,
        "tenant_label": result["label"],
        "tenant_slug": request.client_id,
        "nav_permission": nav_permission,
        "token_validity": {
            "days": request.days,
            "hours": 0,
            "minutes": 0,
            "seconds": 0,
            "total_seconds": timedelta(days=request.days).total_seconds()
        },
        "expires_at": (datetime.now(timezone.utc) + timedelta(days=request.days)).isoformat()
    }




